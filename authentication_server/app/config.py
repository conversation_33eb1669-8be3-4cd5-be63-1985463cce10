import os
import secrets
from typing import Dict, List, Optional

# JWT settings
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", secrets.token_hex(32))
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_EXPIRE_MINUTES", "60"))

# AWS settings
AWS_REGION = os.getenv("AWS_REGION", "ap-southeast-2")

# API settings
API_KEYS: Dict[str, Dict] = {
    # Add default API keys here or load from environment/secure storage
    os.getenv("DEFAULT_API_KEY", "dev-api-key-change-me"): {
        "client_id": "default_client", # Changed for clarity
        "scopes": ["read", "write"]
    },
    os.getenv("GRAFANA_API_KEY", "grafana-default-key-please-change"): { # New Grafana Key
        "client_id": "grafana_service",
        "scopes": ["s3:read", "s3:list"], # Example scopes Grafana might need
        "trusted_for_direct_access": True  # Mark as trusted for direct access
    }
}

# Server settings
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8000")) 