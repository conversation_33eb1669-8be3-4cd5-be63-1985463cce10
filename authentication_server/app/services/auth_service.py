from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, Optional

from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import API<PERSON>eyHeader, OAuth2PasswordBearer
import logging

from ..config import JWT_SECRET_KEY, JWT_ALGORITHM, JWT_ACCESS_TOKEN_EXPIRE_MINUTES, API_KEYS

logger = logging.getLogger(__name__)

# --- Existing and New Authentication Schemes ---
# For API Key authentication via /auth/token endpoint
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

# For optional JWT Bearer token in the flexible authentication flow
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="/auth/token", auto_error=False)
# For optional X-API-Key in the flexible authentication flow
api_key_header_optional = APIKeyHeader(name="X-API-Key", auto_error=False)


# --- API Key Validation Logic ---
def _lookup_api_key(api_key_value: str) -> Optional[Dict]:
    """
    Internal helper to look up an API key and return its details.
    Returns None if the key is not found.
    """
    return API_KEYS.get(api_key_value)

async def get_api_key(api_key: str = Depends(api_key_header)) -> Dict:
    """
    Validate the API key from the header for the /auth/token endpoint.
    This dependency ensures the X-API-Key header is present and valid.
    """
    client_info = _lookup_api_key(api_key)
    if not client_info:
        logger.warning(f"Invalid API key attempt for token generation: {api_key[:5]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "APIKey"},
        )
    return client_info


# --- Flexible Authentication Dependency ---
async def get_current_client_flexible_auth(
    token: Optional[str] = Depends(oauth2_scheme_optional),
    api_key_value: Optional[str] = Depends(api_key_header_optional)
) -> Dict:
    """
    Authenticates a client via JWT (Authorization: Bearer) or a trusted API Key (X-API-Key).
    JWT is checked first. If JWT fails or is not present, a trusted API key is checked.
    The API key must be configured with `"trusted_for_direct_access": True`.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials. Provide a valid Bearer token or a trusted X-API-Key.",
        headers={"WWW-Authenticate": "Bearer, X-API-Key"},
    )

    # 1. Try JWT authentication
    if token:
        try:
            payload = verify_jwt_token(token)
            logger.debug(f"Authenticated client '{payload.get('sub')}' using JWT.")
            return payload  # Successfully authenticated with JWT
        except HTTPException as e:
            # Log JWT validation error, but fall through to API key check
            logger.debug(f"JWT validation failed: {e.detail}. Attempting API key authentication.")
            pass

    # 2. Try API Key authentication (if JWT failed or was not provided)
    if api_key_value:
        client_info = _lookup_api_key(api_key_value)
        if client_info:
            if client_info.get("trusted_for_direct_access") is True:
                logger.debug(f"Authenticated client '{client_info.get('client_id')}' using trusted API Key.")
                return client_info  # Successfully authenticated with a trusted API Key
            else:
                logger.warning(
                    f"API key for client '{client_info.get('client_id')}' is valid but not marked as trusted_for_direct_access."
                )
        else: # API key string was provided but it's not in API_KEYS
            logger.warning(f"Invalid API key provided for direct access: {api_key_value[:5]}...")


    # 3. If both methods fail
    logger.warning("Flexible authentication failed: No valid JWT or trusted API Key provided.")
    raise credentials_exception


# --- JWT Creation and Verification (Existing Functions) ---
def create_jwt_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT token based on the provided data
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
    })
    
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_jwt_token(token: str) -> Dict:
    """
    Verify a JWT token and return the decoded payload
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        # Ensure 'sub' (subject) claim is present, common practice for identifying the user/client
        if "sub" not in payload: 
            logger.error(f"Token missing 'sub' claim: {token[:10]}...")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token content: missing subject",
                headers={"WWW-Authenticate": "Bearer error=\"invalid_token\""},
            )
        return payload
    except JWTError as e:
        logger.error(f"JWTError during token validation: {e} for token {token[:10]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Could not validate credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer error=\"invalid_token\""},
        )