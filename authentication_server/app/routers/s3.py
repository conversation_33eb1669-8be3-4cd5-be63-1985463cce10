from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Header, Query, Path
from fastapi.responses import Response, JSONResponse
from typing import List, Optional, Dict # Added Dict
from ..services.s3_service import S3Service
from ..services.auth_service import get_current_client_flexible_auth # Changed import
from pydantic import BaseModel, constr
import re

router = APIRouter(prefix="/s3", tags=["S3 Operations"])

# Model for list files response
class S3FileInfo(BaseModel):
    key: str
    size: int
    last_modified: str

class S3ListResponse(BaseModel):
    bucket: str
    files: List[S3FileInfo]
    


# Bucket name validation - only allow alphanumeric, dots, and hyphens
# Regex pattern for bucket name validation
BUCKET_NAME_PATTERN = r'^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$'

@router.get("/buckets/{bucket_name}/files", response_model=S3ListResponse)
async def list_files(
    bucket_name: str = Path(..., description="S3 bucket name"),
    prefix: Optional[str] = Query("", description="Optional prefix filter"),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    List files in the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    files = s3_service.list_files(bucket_name=bucket_name, prefix=prefix)
    return {"bucket": bucket_name, "files": files}

@router.get("/buckets/{bucket_name}/files/{file_path:path}")
async def download_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    file_path: str = Path(..., description="Path to the file"),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Download a file from the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    file_content = s3_service.download_file(bucket_name=bucket_name, file_path=file_path)
    
    # Determine content type (basic implementation)
    content_type = "application/octet-stream"
    if file_path.endswith(".json"):
        content_type = "application/json"
    elif file_path.endswith(".txt"):
        content_type = "text/plain"
    elif file_path.endswith(".html"):
        content_type = "text/html"
    elif file_path.endswith(".pdf"):
        content_type = "application/pdf"
    elif file_path.endswith(".jpg") or file_path.endswith(".jpeg"):
        content_type = "image/jpeg"
    elif file_path.endswith(".png"):
        content_type = "image/png"
    
    return Response(content=file_content, media_type=content_type)

@router.post("/buckets/{bucket_name}/files/{file_path:path}")
async def upload_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    file_path: str = Path(..., description="Path to save the file"),
    file: UploadFile = File(...),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Upload a file to the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    file_content = await file.read()
    result = s3_service.upload_file(bucket_name=bucket_name, file_path=file_path, file_content=file_content)
    return JSONResponse(content=result)

@router.delete("/buckets/{bucket_name}/files/{file_path:path}")
async def delete_file(
    bucket_name: str = Path(..., description="S3 bucket name"),
    file_path: str = Path(..., description="Path to the file to delete"),
    current_client: Dict = Depends(get_current_client_flexible_auth)
):
    """
    Delete a file from the specified S3 bucket
    """
    # Validate bucket name against regex pattern
    if not re.match(BUCKET_NAME_PATTERN, bucket_name):
        raise HTTPException(status_code=400, detail="Invalid bucket name format")
    
    s3_service = S3Service()
    result = s3_service.delete_file(bucket_name=bucket_name, file_path=file_path)
    return JSONResponse(content=result) 