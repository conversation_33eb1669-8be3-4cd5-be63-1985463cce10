from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict

from ..services.auth_service import get_api_key, create_jwt_token
from ..config import JWT_ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter(prefix="/auth", tags=["Authentication"])

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

@router.post("/token", response_model=TokenResponse)
async def login_for_access_token(client_info: Dict = Depends(get_api_key)):
    """
    Generate a JWT token when provided with a valid API key
    """
    # Create token data using client info from the API key
    token_data = {
        "sub": client_info["client_id"],
        "scopes": client_info["scopes"]
    }
    
    # Create the token
    access_token = create_jwt_token(
        data=token_data,
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert to seconds
    }