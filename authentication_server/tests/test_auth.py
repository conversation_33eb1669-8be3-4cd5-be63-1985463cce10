import pytest
from jose import jwt
import time

from authentication_server.app.config import JWT_SECRET_KEY, JWT_ALGORITHM


def test_health_check(test_client):
    """
    Test the health check endpoint
    """
    response = test_client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}


def test_root_endpoint(test_client):
    """
    Test the root endpoint
    """
    response = test_client.get("/")
    assert response.status_code == 200
    assert "message" in response.json()
    assert "docs_url" in response.json()
    assert "health_url" in response.json()


def test_get_token_valid_api_key(test_client, api_key):
    """
    Test getting a token with a valid API key
    """
    response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert "token_type" in data
    assert "expires_in" in data
    assert data["token_type"] == "bearer"
    
    # Verify the token can be decoded
    payload = jwt.decode(
        data["access_token"], 
        key=JWT_SECRET_KEY, 
        algorithms=[JWT_ALGORITHM]
    )
    assert "sub" in payload
    assert "exp" in payload
    assert "iat" in payload


def test_get_token_invalid_api_key(test_client):
    """
    Test getting a token with an invalid API key
    """
    response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": "invalid-api-key"}
    )
    assert response.status_code == 401
    assert "Invalid API key" in response.json()["detail"]


def test_get_token_missing_api_key(test_client):
    """
    Test getting a token without providing an API key
    """
    response = test_client.post("/auth/token")
    assert response.status_code == 403  # Changed from 422 to 403 to match FastAPI's behavior


def test_token_expiration(test_client, api_key):
    """
    Test that the token expiration works as expected 
    (This is a more advanced test that demonstrates how to manipulate JWT tokens)
    """
    # This test would be more involved in a real system
    # For now, we'll verify the token gets an expiration time in the future
    response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = response.json()["access_token"]
    
    # Decode without verification
    payload = jwt.decode(token, key=JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
    
    # Check that expiration is in the future
    assert payload["exp"] > time.time() 