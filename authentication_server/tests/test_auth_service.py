import pytest
from jose import jwt
from fastapi import HTTPException
import time
from datetime import datetime, timedelta

from authentication_server.app.services.auth_service import create_jwt_token, verify_jwt_token
from authentication_server.app.config import JWT_SECRET_KEY, JWT_ALGORITHM


def test_create_jwt_token():
    """
    Test creating a JWT token
    """
    test_data = {"sub": "test_client", "scopes": ["read"]}
    token = create_jwt_token(test_data)
    
    # Token should be a non-empty string
    assert isinstance(token, str)
    assert len(token) > 0
    
    # Decode token and verify the data
    payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
    assert payload["sub"] == "test_client"
    assert payload["scopes"] == ["read"]
    assert "exp" in payload
    assert "iat" in payload


def test_create_jwt_token_with_expiry():
    """
    Test creating a JWT token with a specific expiry time
    """
    test_data = {"sub": "test_client", "scopes": ["read"]}
    expires_delta = timedelta(minutes=30)
    
    token = create_jwt_token(test_data, expires_delta=expires_delta)
    payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
    
    # Check that expiry time is approximately 30 minutes from now
    expected_exp = datetime.utcnow() + expires_delta
    token_exp = datetime.utcfromtimestamp(payload["exp"])
    
    # Allow for a small difference due to processing time
    diff = abs((expected_exp - token_exp).total_seconds())
    assert diff < 5  # Difference should be very small


def test_verify_valid_jwt_token():
    """
    Test verifying a valid JWT token
    """
    test_data = {"sub": "test_client", "scopes": ["read"]}
    token = create_jwt_token(test_data)
    
    # Verify should return the token payload
    payload = verify_jwt_token(token)
    assert payload["sub"] == "test_client"
    assert payload["scopes"] == ["read"]


def test_verify_invalid_jwt_token():
    """
    Test verifying an invalid JWT token
    """
    # Create an invalid token (wrong signature)
    test_data = {"sub": "test_client"}
    token = jwt.encode(test_data, "wrong-secret", algorithm=JWT_ALGORITHM)
    
    # Should raise an HTTPException
    with pytest.raises(HTTPException) as exc_info:
        verify_jwt_token(token)
    
    assert exc_info.value.status_code == 401
    assert "Could not validate credentials" in exc_info.value.detail


def test_verify_expired_token():
    """
    Test verifying an expired token
    """
    # Create a token that's already expired
    test_data = {
        "sub": "test_client",
        "exp": datetime.utcnow() - timedelta(minutes=5)
    }
    token = jwt.encode(test_data, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    # Should raise an HTTPException
    with pytest.raises(HTTPException) as exc_info:
        verify_jwt_token(token)
    
    assert exc_info.value.status_code == 401


def test_verify_token_missing_sub():
    """
    Test verifying a token missing the subject claim
    """
    # Create a token without a 'sub' claim
    test_data = {"scopes": ["read"]}
    token = jwt.encode(test_data, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    # Should raise an HTTPException
    with pytest.raises(HTTPException) as exc_info:
        verify_jwt_token(token)
    
    assert exc_info.value.status_code == 401
    assert "Invalid token content" in exc_info.value.detail 