import pytest
from unittest.mock import patch, <PERSON>Mock, ANY
import io
from fastapi import HTTPException
from botocore.exceptions import ClientError

from authentication_server.app.services.s3_service import S3Service


@pytest.fixture
def s3_service(mock_s3_client):
    """
    Create an S3Service with mocked boto3 client
    """
    return S3Service()


def test_download_file_success(s3_service, mock_s3_client, mock_s3_get_object_response):
    """
    Test downloading a file successfully
    """
    mock_s3_client.get_object.return_value = mock_s3_get_object_response
    
    result = s3_service.download_file("test-bucket", "test-file.txt")
    
    # Verify result
    assert result == b"test file content"
    
    # Verify the S3 client was called correctly
    mock_s3_client.get_object.assert_called_once_with(
        Bucket="test-bucket",
        Key="test-file.txt"
    )


def test_download_file_not_found(s3_service, mock_s3_client):
    """
    Test downloading a file that doesn't exist
    """
    # Configure mock to raise NoSuch<PERSON>ey error
    error_response = {
        "Error": {
            "Code": "NoSuchKey",
            "Message": "The specified key does not exist."
        }
    }
    mock_s3_client.get_object.side_effect = ClientError(error_response, "GetObject")
    
    with pytest.raises(HTTPException) as exc_info:
        s3_service.download_file("test-bucket", "non-existent-file.txt")
    
    assert exc_info.value.status_code == 404
    assert "File not found" in exc_info.value.detail


def test_upload_file_success(s3_service, mock_s3_client):
    """
    Test uploading a file successfully
    """
    file_content = b"test content to upload"
    result = s3_service.upload_file("test-bucket", "test-upload.txt", file_content)
    
    # Verify result
    assert result["success"] is True
    assert result["bucket"] == "test-bucket"
    assert result["file_path"] == "test-upload.txt"
    
    # Verify the S3 client was called correctly
    mock_s3_client.upload_fileobj.assert_called_once()


def test_upload_file_access_denied(s3_service, mock_s3_client):
    """
    Test uploading a file with access denied
    """
    # Configure mock to raise AccessDenied error
    error_response = {
        "Error": {
            "Code": "AccessDenied",
            "Message": "Access Denied"
        }
    }
    mock_s3_client.upload_fileobj.side_effect = ClientError(error_response, "UploadFileobj")
    
    with pytest.raises(HTTPException) as exc_info:
        s3_service.upload_file("test-bucket", "test-upload.txt", b"test content")
    
    assert exc_info.value.status_code == 403
    assert "Access denied" in exc_info.value.detail


def test_list_files_success(s3_service, mock_s3_client, mock_s3_list_objects_response):
    """
    Test listing files successfully
    """
    mock_s3_client.list_objects_v2.return_value = mock_s3_list_objects_response
    
    result = s3_service.list_files("test-bucket", "test-prefix")
    
    # Verify result
    assert len(result) == 2
    assert result[0]["key"] == "test_file1.txt"
    assert result[1]["key"] == "test_file2.json"
    
    # Verify the S3 client was called correctly
    mock_s3_client.list_objects_v2.assert_called_once_with(
        Bucket="test-bucket",
        Prefix="test-prefix"
    )


def test_list_files_empty_bucket(s3_service, mock_s3_client):
    """
    Test listing files in an empty bucket
    """
    # Configure mock to return empty response
    mock_s3_client.list_objects_v2.return_value = {}
    
    result = s3_service.list_files("test-bucket")
    
    # Should return empty list
    assert result == []


def test_delete_file_success(s3_service, mock_s3_client):
    """
    Test deleting a file successfully
    """
    result = s3_service.delete_file("test-bucket", "test-file.txt")
    
    # Verify result
    assert result["success"] is True
    assert result["bucket"] == "test-bucket"
    assert result["file_path"] == "test-file.txt"
    
    # Verify the S3 client was called correctly
    mock_s3_client.delete_object.assert_called_once_with(
        Bucket="test-bucket",
        Key="test-file.txt"
    ) 